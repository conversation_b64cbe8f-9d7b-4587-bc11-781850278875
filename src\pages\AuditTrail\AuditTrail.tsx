import Breadcrumb from '../../components/Breadcrumb/Breadcrumb';

const AuditTrail = () => {
  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-500">Settings</h1>
          <Breadcrumb pageName="Settings" />
        </div>
      </div>
      <p className="text-gray-600">Configure application settings and preferences</p>
<button 
  data-popover-target="menu"
  className="rounded-md bg-slate-800 py-2 px-4 border border-transparent text-center text-sm text-white transition-all shadow-md hover:shadow-lg focus:bg-slate-700 focus:shadow-none active:bg-slate-700 hover:bg-slate-700 active:shadow-none disabled:pointer-events-none disabled:opacity-50 disabled:shadow-none ml-2" type="button">
  Open Menu
</button>
<ul
  role="menu"
  data-popover="menu"
  data-popover-placement="bottom"
  className="absolute z-10 min-w-[180px] overflow-auto rounded-lg border border-slate-200 bg-white p-1.5 shadow-lg shadow-sm focus:outline-none"
>
  <li
    role="menuitem"
    className="cursor-pointer text-slate-800 flex w-full text-sm items-center rounded-md p-3 transition-all hover:bg-slate-100 focus:bg-slate-100 active:bg-slate-100"
  >
    {`Menu Item 1`}
  </li>
  <li
    role="menuitem"
    className="cursor-pointer text-slate-800 flex w-full text-sm items-center rounded-md p-3 transition-all hover:bg-slate-100 focus:bg-slate-100 active:bg-slate-100"
  >
    {`Menu Item 2`}
  </li>
  <li
    role="menuitem"
    className="cursor-pointer text-slate-800 flex w-full text-sm items-center rounded-md p-3 transition-all hover:bg-slate-100 focus:bg-slate-100 active:bg-slate-100"
  >
    {`Menu Item 3`}
  </li>
</ul>
    </div>
  );
};

export default AuditTrail;