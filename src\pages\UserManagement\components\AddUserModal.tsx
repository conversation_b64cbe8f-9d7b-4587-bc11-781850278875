import React, { useState } from 'react';
import Modal from '../../../components/Modal';
import { Button } from '../../../components/Button';
import Dropdown from '../../../components/Dropdown';

interface AddUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (userData: UserFormData) => void;
}

interface UserFormData {
  firstName: string;
  lastName: string;
  email: string;
  manager: string;
  department: string;
  role: string;
  permissions: {
    viewExpenses: boolean;
    submitExpenses: boolean;
    editOwnExpenses: boolean;
    approveExpenses: boolean;
    viewAllExpenses: boolean;
    editAllExpenses: boolean;
    manageUsers: boolean;
    generateReports: boolean;
  };
}

const AddUserModal: React.FC<AddUserModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
}) => {
  const [formData, setFormData] = useState<UserFormData>({
    firstName: '',
    lastName: '',
    email: '',
    manager: '',
    department: '',
    role: '',
    permissions: {
      viewExpenses: false,
      submitExpenses: false,
      editOwnExpenses: false,
      approveExpenses: false,
      viewAllExpenses: false,
      editAllExpenses: false,
      manageUsers: false,
      generateReports: false,
    },
  });

  // Mock data - replace with actual data from your API
  const managers = ['John Smith', 'Sarah Johnson', 'Mike <PERSON>', 'Lisa Wilson'];
  const departments = ['Finance', 'HR', 'IT', 'Marketing', 'Sales'];
  const roles = ['Employee', 'Manager', 'Admin', 'Finance Manager'];

  const handleInputChange = (field: keyof UserFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handlePermissionChange = (permission: keyof UserFormData['permissions']) => {
    setFormData(prev => ({
      ...prev,
      permissions: {
        ...prev.permissions,
        [permission]: !prev.permissions[permission],
      },
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
    onClose();
    // Reset form
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      manager: '',
      department: '',
      role: '',
      permissions: {
        viewExpenses: false,
        submitExpenses: false,
        editOwnExpenses: false,
        approveExpenses: false,
        viewAllExpenses: false,
        editAllExpenses: false,
        manageUsers: false,
        generateReports: false,
      },
    });
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Add New User"
      className="max-w-4xl"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* First Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              First Name
            </label>
            <input
              type="text"
              value={formData.firstName}
              onChange={(e) => handleInputChange('firstName', e.target.value)}
              placeholder="Enter first name"
              className="w-full px-4 py-3 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>

          {/* Manager */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Manager
            </label>
            <Dropdown
              value={formData.manager}
              onChange={(value) => handleInputChange('manager', value)}
              options={managers}
              label="Manager"
              placeholder="Select manager"
            />
          </div>

          {/* Last Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Last Name
            </label>
            <input
              type="text"
              value={formData.lastName}
              onChange={(e) => handleInputChange('lastName', e.target.value)}
              placeholder="Enter last name"
              className="w-full px-4 py-3 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>

          {/* Department */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Department
            </label>
            <Dropdown
              value={formData.department}
              onChange={(value) => handleInputChange('department', value)}
              options={departments}
              label="Department"
              placeholder="Select department"
            />
          </div>

          {/* Email Address */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email Address
            </label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              placeholder="Enter email address"
              className="w-full px-4 py-3 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>

          {/* Role */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Role
            </label>
            <Dropdown
              value={formData.role}
              onChange={(value) => handleInputChange('role', value)}
              options={roles}
              label="Role"
              placeholder="Select role"
            />
          </div>
        </div>

        {/* Permissions Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 pt-6">
          {/* Role Permissions */}
          <div>
            <h3 className="text-lg font-medium text-blue-600 mb-2">Role Permissions</h3>
            <p className="text-sm text-gray-600 mb-4">
              Default permissions for selected role will be applied.
              You can customize permissions below.
            </p>
            <div className="space-y-3">
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={formData.permissions.viewExpenses}
                  onChange={() => handlePermissionChange('viewExpenses')}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">View Expenses</span>
              </label>

              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={formData.permissions.submitExpenses}
                  onChange={() => handlePermissionChange('submitExpenses')}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">Submit Expenses</span>
              </label>

              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={formData.permissions.editOwnExpenses}
                  onChange={() => handlePermissionChange('editOwnExpenses')}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">Edit Own Expenses</span>
              </label>
            </div>
          </div>

          {/* Customize Permissions */}
          <div>
            <h3 className="text-lg font-medium text-blue-600 mb-2">Customize Permissions (Optional)</h3>
            <div className="space-y-3">
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={formData.permissions.approveExpenses}
                  onChange={() => handlePermissionChange('approveExpenses')}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">Approve Expenses</span>
              </label>

              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={formData.permissions.viewAllExpenses}
                  onChange={() => handlePermissionChange('viewAllExpenses')}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">View All Expenses</span>
              </label>

              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={formData.permissions.editAllExpenses}
                  onChange={() => handlePermissionChange('editAllExpenses')}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">Edit All Expenses</span>
              </label>

              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={formData.permissions.manageUsers}
                  onChange={() => handlePermissionChange('manageUsers')}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">Manage Users</span>
              </label>

              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={formData.permissions.generateReports}
                  onChange={() => handlePermissionChange('generateReports')}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">Generate Reports</span>
              </label>
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end pt-6">
          <Button
            type="submit"
            className="bg-teal-700 hover:bg-teal-800 text-white px-8 py-3 rounded-md font-medium"
          >
            Submit
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default AddUserModal;