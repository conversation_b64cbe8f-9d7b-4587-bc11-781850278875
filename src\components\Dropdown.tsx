import { ChevronDown } from "lucide-react";

interface DropdownProps {
  value: string;
  onChange: (value: string) => void;
  options: string[];
  label: string;
  className?: string;
  placeholder?: string;
  fullWidth?: boolean;
}

export default function Dropdown({
  value,
  onChange,
  options,
  label,
  className = "",
  placeholder,
  fullWidth = true,
}: DropdownProps) {
  const displayPlaceholder = placeholder || `Select ${label.toLowerCase()}`;

  return (
    <div className={`relative ${fullWidth ? 'w-full' : 'w-fit'} ${className}`}>
      <select
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="appearance-none bg-white border border-gray-200 text-gray-700 px-4 py-3 pr-10 rounded-md shadow-sm outline-none transition w-full"
      >
        <option value="">{displayPlaceholder}</option>
        {options.map((opt) => (
          <option key={opt} value={opt}>
            {opt}
          </option>
        ))}
      </select>
      <ChevronDown className="absolute right-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-500 pointer-events-none" />
    </div>
  );
}
