import { useState, useRef } from "react";
import { ChevronDown } from "lucide-react";

interface DropdownProps {
  value: string;
  onChange: (value: string) => void;
  options: string[];
  label: string;
  className?: string;
  placeholder?: string;
  fullWidth?: boolean;
}

export default function Dropdown({
  value,
  onChange,
  options,
  label,
  className = "",
  placeholder,
  fullWidth = false,
}: DropdownProps) {
  const [open, setOpen] = useState(false);
  const buttonRef = useRef<HTMLButtonElement>(null);

  const displayLabel = value || placeholder || `Select ${label.toLowerCase()}`;

  const toggleDropdown = () => setOpen((prev) => !prev);

  const handleSelect = (opt: string) => {
    onChange(opt);
    setOpen(false);
  };

  return (
    <div className={`relative ${fullWidth ? "w-full" : "w-fit"} ${className}`}>
      <button
        ref={buttonRef}
        type="button"
        onClick={toggleDropdown}
        className={`cursor-pointer py-3 px-4 inline-flex items-center justify-between text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 focus:outline-none focus:bg-gray-50 dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 ${
          fullWidth ? "w-full" : "w-fit"
        }`}
      >
        <span>{displayLabel}</span>
        <ChevronDown
          className={`w-4 h-4 ml-2 transition-transform duration-200 ${
            open ? "rotate-180" : ""
          }`}
        />
      </button>

      <div
        className={`absolute z-10 mt-2 min-w-full bg-white border border-gray-200 rounded-lg shadow-md dark:bg-neutral-800 dark:border-neutral-700 overflow-hidden transition-all duration-200 ${
          open
            ? "opacity-100 scale-100 visible"
            : "opacity-0 scale-95 invisible"
        }`}
      >
        <div className="p-1 max-h-60 overflow-y-auto">
          {options.map((opt) => (
            <div
              key={opt}
              onClick={() => handleSelect(opt)}
              className={`cursor-pointer flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700 ${
                value === opt ? "bg-gray-100 dark:bg-neutral-700" : ""
              }`}
            >
              {opt}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
