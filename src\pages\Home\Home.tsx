import React from 'react';
import Breadcrumb from '../../components/Breadcrumb/Breadcrumb';
import { TrendingUp, TrendingDown } from 'lucide-react';
import {
  Line<PERSON>hart,
  Line,
  XAxis,
  Responsive<PERSON>ontainer,
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
} from 'recharts';

const dashboardStats = {
  totalExpensesClaimed: 1250000,
  totalReimbursementsPaid: 1100000,
  pendingRequests: 75,
  averageReimburseTime: 3,
  expensesClaimedChange: 10,
  reimbursementsPaidChange: 8,
  pendingRequestsChange: -5,
  averageTimeChange: -1,
};

const expenseTrendData = [
  { month: 'SEP', value: 115 },
  { month: 'OCT', value: 95 },
  { month: 'NOV', value: 165 },
  { month: 'DEC', value: 95 },
  { month: 'JAN', value: 110 },
  { month: 'FEB', value: 115 },
];

const recentActivity = [
  {
    id: '1',
    title: 'New Expense Report',
    submittedBy: '<PERSON>',
    date: '2024-07-28',
  },
  {
    id: '2',
    title: 'New Expense Report',
    submittedBy: '<PERSON>',
    date: '2024-07-28',
  },
  {
    id: '3',
    title: 'New Expense Report',
    submittedBy: '<PERSON> <PERSON>',
    date: '2024-07-28',
  },
  {
    id: '4',
    title: 'New Expense Report',
    submittedBy: 'Emma Bennett',
    date: '2024-07-28',
  },
];

const userActivity = [
  {
    id: '1',
    type: 'user_registered',
    title: 'New user registered: Sophia Turner',
    subtitle: '2 hours ago',
    icon: 'user',
  },
  {
    id: '2',
    type: 'expense_submitted',
    title: 'High-value expense report submitted by Liam Harper',
    subtitle: '4 hours ago',
    icon: 'file',
  },
  {
    id: '3',
    type: 'reimbursement_approved',
    title: 'Reimbursement request approved for Ava Bennett',
    subtitle: '6 hours ago',
    icon: 'check',
  },
  {
    id: '4',
    type: 'system_updated',
    title: 'System settings updated by Admin',
    subtitle: '1 day ago',
    icon: 'settings',
  },
  {
    id: '5',
    type: 'policy_updated',
    title: 'Expense policy updated',
    subtitle: '2 days ago',
    icon: 'file',
  },
];

const policyCompliance = {
  percentage: 66,
};

const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

const formatNumber = (num: number): string => {
  return new Intl.NumberFormat('en-US').format(num);
};

const StatCard: React.FC<{
  title: string;
  value: string;
  change: number;
  isTime?: boolean;
}> = ({ title, value, change, isTime = false }) => {
  const changeColor = change > 0 ? 'text-green-500' : 'text-red-500';
  const changeIcon =
    change > 0 ? (
      <TrendingUp className="w-4 h-4" />
    ) : (
      <TrendingDown className="w-4 h-4" />
    );
  const changeText =
    change > 0
      ? `+${change}${isTime ? ' day' : '%'}`
      : `${change}${isTime ? ' day' : '%'}`;

  return (
    <div className="bg-white rounded-xl p-6 shadow-sm h-[120px] flex flex-col justify-between">
      <h3 className="text-gray-400 text-sm">{title}</h3>
      <div className="flex items-end justify-between">
        <div className="text-3xl font-bold text-gray-900">{value}</div>
        <div className={`flex items-center gap-1 text-sm ${changeColor}`}>
          {changeIcon}
          {changeText}
        </div>
      </div>
    </div>
  );
};

const ExpenseTrendChart: React.FC = () => {
  return (
    <div className="bg-white rounded-xl p-6 shadow-sm h-[380px]">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Expense Trend</h3>
        <div className="flex items-center gap-4 text-sm text-gray-600">
          <div className="flex items-center gap-2">
            <div className="w-3 h-0.5 bg-teal-500 rounded-full"></div>
            <span>This Month</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-0.5 bg-teal-700 rounded-full"></div>
            <span>Past Month</span>
          </div>
        </div>
      </div>

      <div className="mb-4">
        <div className="text-4xl font-bold text-gray-900">$37.5K</div>
        <div className="text-sm text-gray-500 mb-1">Total Spent</div>
        <div className="flex items-center gap-1 text-sm text-green-500 mb-2">
          <TrendingUp className="w-4 h-4" />
          +2.45%
        </div>
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <span className="text-sm text-gray-600">On track</span>
        </div>
      </div>

      <div className="h-[220px] relative">
        <div className="absolute top-[80px] left-[100px] bg-teal-500 text-white text-xs px-2 py-1 rounded">
          $108.00
        </div>
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={expenseTrendData}
            margin={{ top: 20, right: 30, left: 0, bottom: 0 }}
          >
            <XAxis
              dataKey="month"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: '#9ca3af' }}
            />
            <Line
              type="monotone"
              dataKey="value"
              stroke="#10b981"
              strokeWidth={3}
              dot={(props) => {
                const { cx, cy, index } = props;
                return index === 2 ? (
                  <circle cx={cx} cy={cy} r={6} fill="#10b981" stroke="white" strokeWidth={2} />
                ) : null;
              }}
              activeDot={{ r: 8 }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

const PolicyComplianceChart: React.FC<{ percentage: number }> = ({
  percentage,
}) => {
  const remainingPercentage = 100 - percentage;

  const pieData = [
    {
      name: 'Within Policy',
      value: percentage,
      color: '#10b981',
    },
    {
      name: 'Outside Policy',
      value: remainingPercentage,
      color: '#6b7280',
    },
  ];

  return (
    <div className="bg-white rounded-xl p-6 shadow-sm h-[380px]">
      <h3 className="text-lg font-semibold text-gray-900 mb-6">
        Policy Compliance
      </h3>

      <div className="flex flex-col items-center">
        <div className="relative w-48 h-48 mb-2">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={pieData}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={80}
                startAngle={90}
                endAngle={450}
                dataKey="value"
              >
                {pieData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
            </PieChart>
          </ResponsiveContainer>

          <div className="absolute inset-0 flex flex-col items-center justify-center">
            <div className="text-4xl font-bold text-gray-900">
              {percentage}%
            </div>
            <div className="text-sm text-gray-600 text-center">
              Within Policy
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const ActivityList: React.FC = () => {
  return (
    <div className="bg-white rounded-xl p-6 shadow-sm">
      <h3 className="text-lg font-semibold text-gray-900 mb-6">
        Recent Activity
      </h3>

      <div className="space-y-4">
        <div className="grid grid-cols-4 gap-4 text-sm font-medium text-gray-600 pb-2 border-b">
          <div>Activity</div>
          <div>Details</div>
          <div>Date</div>
          <div>Actions</div>
        </div>

        {recentActivity.map((activity) => (
          <div
            key={activity.id}
            className="grid grid-cols-4 gap-4 text-sm py-3 border-b border-gray-100 last:border-0"
          >
            <div className="font-medium text-gray-900">{activity.title}</div>
            <div className="text-gray-600">
              Submitted by {activity.submittedBy}
            </div>
            <div className="text-gray-600">{activity.date}</div>
            <div>
              <button className="text-teal-500 hover:text-teal-600 font-medium">
                view
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const UserActivityFeed: React.FC = () => {
  return (
    <div className="bg-white rounded-xl p-6 shadow-sm">
      <h3 className="text-lg font-semibold text-gray-900 mb-6">
        User Activity
      </h3>
      <div className="relative pl-8">
        {/* Vertical timeline line */}
        <div className="absolute left-4 top-1 bottom-0 w-[1px] bg-gray-200"></div>
        
        {userActivity.map((activity, index) => (
          <div key={activity.id} className="mb-6 relative">
            {/* Timeline dot */}
            <div className="absolute -left-8 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-gray-600 z-10">
              {activity.icon === 'user' && (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                </svg>
              )}
              {activity.icon === 'file' && (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clipRule="evenodd" />
                </svg>
              )}
              {activity.icon === 'check' && (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              )}
              {activity.icon === 'settings' && (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                </svg>
              )}
            </div>
            
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900">
                {activity.title}
              </p>
              <p className="text-xs text-gray-500 mt-1">{activity.subtitle}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const Home: React.FC = () => {
  return (
    <div className="p-6 bg-gray-50 min-h-screen">

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatCard
          title="Total Expenses Claimed"
          value={formatCurrency(dashboardStats.totalExpensesClaimed)}
          change={dashboardStats.expensesClaimedChange}
        />
        <StatCard
          title="Total Reimbursements Paid"
          value={formatCurrency(dashboardStats.totalReimbursementsPaid)}
          change={dashboardStats.reimbursementsPaidChange}
        />
        <StatCard
          title="Pending Requests"
          value={formatNumber(dashboardStats.pendingRequests)}
          change={dashboardStats.pendingRequestsChange}
        />
        <StatCard
          title="Average Reimburse Time"
          value={`${dashboardStats.averageReimburseTime} days`}
          change={dashboardStats.averageTimeChange}
          isTime={true}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <div className="lg:col-span-2">
          <ExpenseTrendChart />
        </div>
        <div>
          <PolicyComplianceChart percentage={policyCompliance.percentage} />
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <ActivityList />
        </div>
        <div>
          <UserActivityFeed />
        </div>
      </div>
    </div>
  );
};

export default Home;